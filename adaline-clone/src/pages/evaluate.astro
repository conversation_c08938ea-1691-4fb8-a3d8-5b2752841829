---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Evaluate | Adaline">
  <Header />
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-green-50 to-blue-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Evaluate
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            Comprehensive evaluation framework to test and validate your AI models with robust metrics, automated testing pipelines, and continuous monitoring.
          </p>
          <a href="https://app.adaline.ai" 
             class="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
            Start Evaluating
            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div class="w-full h-96 bg-gradient-to-br from-green-100 to-blue-100 rounded-2xl flex items-center justify-center">
            <span class="text-gray-600">Evaluation Dashboard Preview</span>
          </div>
          <div>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Automated Testing & Validation
            </h2>
            <p class="text-lg text-gray-600 mb-8">
              Set up comprehensive evaluation pipelines that automatically test your AI models against various metrics and benchmarks to ensure consistent performance.
            </p>
            <ul class="space-y-4">
              <li class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Custom evaluation metrics</span>
              </li>
              <li class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Automated testing pipelines</span>
              </li>
              <li class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Performance benchmarking</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout>
