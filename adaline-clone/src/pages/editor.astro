---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Editor | Adaline">
  <Header />
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center max-w-4xl mx-auto mb-16">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Editor
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            Write, tune, and iterate your prompts in one powerful workspace. Select any model, adjust its parameters, and unlock precise AI outputs instantly.
          </p>
          <a href="https://app.adaline.ai" 
             class="inline-flex items-center bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors font-medium">
            Get Started
            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>

        <!-- Product Preview -->
        <div class="relative max-w-6xl mx-auto">
          <div class="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
            <div class="bg-gray-50 border-b border-gray-200 px-6 py-4">
              <div class="flex items-center space-x-3">
                <div class="flex space-x-2">
                  <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div class="text-sm text-gray-600">Adaline Editor</div>
              </div>
            </div>
            <div class="p-8">
              <div class="w-full h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center">
                <span class="text-gray-500">Product Layout Image</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Trust Indicators -->
        <div class="mt-16 text-center">
          <p class="text-sm text-gray-500 mb-6">Trusted by</p>
          <div class="flex justify-center items-center space-x-8 opacity-60">
            <div class="w-24 h-8 bg-gray-200 rounded"></div>
            <div class="w-24 h-8 bg-gray-200 rounded"></div>
            <div class="w-24 h-8 bg-gray-200 rounded"></div>
            <div class="w-24 h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            One Workspace for All Your Prompt Needs.
          </h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
          <!-- Prompt Editing -->
          <div class="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
            <div class="w-full md:w-1/2">
              <div class="w-full h-64 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                <span class="text-gray-600">Prompt Editing</span>
              </div>
            </div>
            <div class="w-full md:w-1/2">
              <p class="text-sm text-blue-600 font-medium mb-2">Prompt Editing</p>
              <h3 class="text-2xl font-bold text-gray-900 mb-4">Sketch the Perfect Prompt</h3>
              <p class="text-gray-600">
                Quickly write, adjust, and perfect prompts until they match exactly what you have in mind.
              </p>
            </div>
          </div>

          <!-- Model Config -->
          <div class="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
            <div class="w-full md:w-1/2">
              <div class="w-full h-64 bg-gradient-to-br from-green-100 to-blue-100 rounded-lg flex items-center justify-center">
                <span class="text-gray-600">Model Config</span>
              </div>
            </div>
            <div class="w-full md:w-1/2">
              <p class="text-sm text-green-600 font-medium mb-2">Model Config</p>
              <h3 class="text-2xl font-bold text-gray-900 mb-4">Tune AI's Behavior</h3>
              <p class="text-gray-600">
                Easily select model versions, adjust key settings, and turn on advanced capabilities with a click.
              </p>
            </div>
          </div>

          <!-- Variables and Tools -->
          <div class="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
            <div class="w-full md:w-1/2">
              <div class="w-full h-64 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center">
                <span class="text-gray-600">Variables and Tools</span>
              </div>
            </div>
            <div class="w-full md:w-1/2">
              <p class="text-sm text-purple-600 font-medium mb-2">Variables and Tools</p>
              <h3 class="text-2xl font-bold text-gray-900 mb-4">Craft Prompts based on Tool and API Calling</h3>
              <p class="text-gray-600">
                Easily integrate function calls and API endpoints into your prompts to fetch live data, perform computations, and deliver context-aware AI responses.
              </p>
            </div>
          </div>

          <!-- Deploy Changes -->
          <div class="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
            <div class="w-full md:w-1/2">
              <div class="w-full h-64 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg flex items-center justify-center">
                <span class="text-gray-600">Deploy Changes</span>
              </div>
            </div>
            <div class="w-full md:w-1/2">
              <p class="text-sm text-orange-600 font-medium mb-2">Deploy Changes</p>
              <h3 class="text-2xl font-bold text-gray-900 mb-4">Seamless Prompt Publishing</h3>
              <p class="text-gray-600">
                Preview changes, pick your environment, and deploy updates without touching a line of code.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 text-center mb-16">FAQs</h2>
        
        <div class="space-y-6">
          <div class="bg-white rounded-lg border border-gray-200">
            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
              <span class="font-semibold text-gray-900">What is a prompt editor and how does it work?</span>
              <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="hidden px-6 pb-4">
              <p class="text-gray-600">A prompt editor is a specialized tool that allows you to create, modify, and optimize prompts for AI language models. It provides an intuitive interface for crafting effective prompts and testing their outputs.</p>
            </div>
          </div>

          <div class="bg-white rounded-lg border border-gray-200">
            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
              <span class="font-semibold text-gray-900">How do I write effective prompts for LLMs?</span>
              <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="hidden px-6 pb-4">
              <p class="text-gray-600">Effective prompts are clear, specific, and provide context. Use examples, specify the desired format, and iterate based on the outputs you receive.</p>
            </div>
          </div>

          <div class="bg-white rounded-lg border border-gray-200">
            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
              <span class="font-semibold text-gray-900">Can I edit or adjust prompts if I don't get the desired results?</span>
              <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div class="hidden px-6 pb-4">
              <p class="text-gray-600">Yes, our editor allows you to continuously refine and adjust your prompts. You can test different variations and see results in real-time.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout>

<script>
  function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('svg');
    
    if (content.classList.contains('hidden')) {
      content.classList.remove('hidden');
      icon.style.transform = 'rotate(180deg)';
    } else {
      content.classList.add('hidden');
      icon.style.transform = 'rotate(0deg)';
    }
  }
</script>
