---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Listen - PJM Africa">
  <Header />
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Listen
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            Tune into weekly podcast episodes featuring voices from across Africa and the diaspora, sharing stories of identity, culture, and opportunity.
          </p>
        </div>
      </div>
    </section>

    <!-- Featured Episode -->
    <section class="py-12 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-gradient-to-r from-african-red/10 to-african-gold/10 rounded-2xl p-8 mb-16">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <div class="flex items-center space-x-2 mb-4">
                <span class="bg-african-red text-white text-xs font-medium px-2.5 py-0.5 rounded">Latest Episode</span>
                <span class="text-sm text-gray-500">Episode 24</span>
              </div>
              <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Voices from the Diaspora: Finding Home
              </h2>
              <p class="text-gray-600 mb-6">
                A powerful conversation with African diaspora members about identity, belonging, and the journey back to their roots.
              </p>
              <div class="flex items-center space-x-4 mb-6">
                <span class="text-sm text-gray-500">52 min</span>
                <span class="text-sm text-gray-500">•</span>
                <span class="text-sm text-gray-500">Released Jan 15, 2025</span>
              </div>
              
              <!-- Streaming Platform Links -->
              <div class="flex flex-wrap gap-3">
                <a href="#" class="flex items-center space-x-2 bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                  </svg>
                  <span>Apple Podcasts</span>
                </a>
                <a href="#" class="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                  </svg>
                  <span>Spotify</span>
                </a>
                <a href="#" class="flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                  </svg>
                  <span>Google Podcasts</span>
                </a>
              </div>
            </div>
            
            <!-- Audio Player -->
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="flex items-center space-x-4 mb-4">
                <div class="w-16 h-16 bg-gradient-to-r from-african-red to-african-gold rounded-lg flex items-center justify-center">
                  <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">Episode 24</h3>
                  <p class="text-sm text-gray-600">Voices from the Diaspora</p>
                </div>
              </div>
              
              <!-- Audio Controls -->
              <div class="space-y-4">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-gradient-to-r from-african-red to-african-gold h-2 rounded-full" style="width: 35%"></div>
                </div>
                <div class="flex justify-between text-sm text-gray-500">
                  <span>18:24</span>
                  <span>52:16</span>
                </div>
                <div class="flex items-center justify-center space-x-4">
                  <button class="p-2 hover:bg-gray-100 rounded-full">
                    <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                    </svg>
                  </button>
                  <button class="p-3 bg-gradient-to-r from-african-red to-african-gold text-white rounded-full hover:from-african-gold hover:to-african-red transition-all">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </button>
                  <button class="p-2 hover:bg-gray-100 rounded-full">
                    <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Recent Episodes -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Recent Episodes
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Weekly conversations exploring African identity, culture, politics, economics, and healing.
          </p>
        </div>

        <!-- Episodes List -->
        <div class="space-y-6">
          <!-- Episode 23 -->
          <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
              <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-r from-african-gold to-african-green rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-white font-bold">23</span>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">Economic Renaissance in Rwanda</h3>
                  <p class="text-sm text-gray-600">45 min • Jan 8, 2025</p>
                </div>
              </div>
              <div class="md:col-span-2">
                <p class="text-gray-600 text-sm">
                  Exploring Rwanda's remarkable economic transformation and what other African nations can learn from their development model.
                </p>
                <div class="flex flex-wrap gap-2 mt-2">
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Economics</span>
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">East Africa</span>
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Development</span>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <button class="p-2 bg-gradient-to-r from-african-gold to-african-green text-white rounded-full hover:from-african-green hover:to-african-gold transition-all">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
                <div class="flex space-x-2">
                  <a href="#" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                    </svg>
                  </a>
                  <a href="#" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Episode 22 -->
          <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
              <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-r from-african-green to-african-red rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-white font-bold">22</span>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">Ubuntu Philosophy in Modern Times</h3>
                  <p class="text-sm text-gray-600">38 min • Jan 1, 2025</p>
                </div>
              </div>
              <div class="md:col-span-2">
                <p class="text-gray-600 text-sm">
                  Understanding the Ubuntu philosophy and how this African concept of interconnectedness can heal our divided world.
                </p>
                <div class="flex flex-wrap gap-2 mt-2">
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Culture</span>
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Philosophy</span>
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Healing</span>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <button class="p-2 bg-gradient-to-r from-african-green to-african-red text-white rounded-full hover:from-african-red hover:to-african-green transition-all">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
                <div class="flex space-x-2">
                  <a href="#" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                    </svg>
                  </a>
                  <a href="#" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Episode 21 -->
          <div class="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
              <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gradient-to-r from-african-red to-african-sunset rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-white font-bold">21</span>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">The Politics of Language in Africa</h3>
                  <p class="text-sm text-gray-600">42 min • Dec 25, 2024</p>
                </div>
              </div>
              <div class="md:col-span-2">
                <p class="text-gray-600 text-sm">
                  Examining how colonial languages versus indigenous languages shape African identity and political discourse.
                </p>
                <div class="flex flex-wrap gap-2 mt-2">
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Politics</span>
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Identity</span>
                  <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">Language</span>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <button class="p-2 bg-gradient-to-r from-african-red to-african-sunset text-white rounded-full hover:from-african-sunset hover:to-african-red transition-all">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
                <div class="flex space-x-2">
                  <a href="#" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                    </svg>
                  </a>
                  <a href="#" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div class="text-center mt-12">
          <button class="bg-gradient-to-r from-african-red to-african-gold text-white px-8 py-3 rounded-lg hover:from-african-gold hover:to-african-red transition-all font-medium">
            Load More Episodes
          </button>
        </div>
      </div>
    </section>

    <!-- Subscribe Section -->
    <section class="py-20 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
          Never Miss an Episode
        </h2>
        <p class="text-xl text-gray-600 mb-8">
          Subscribe to the PJM Africa podcast on your favorite platform and join the conversation.
        </p>
        
        <!-- Platform Links -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
          <a href="#" class="flex items-center space-x-3 bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
            </svg>
            <span>Apple Podcasts</span>
          </a>
          <a href="#" class="flex items-center space-x-3 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
            </svg>
            <span>Spotify</span>
          </a>
          <a href="#" class="flex items-center space-x-3 bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
            </svg>
            <span>Google Podcasts</span>
          </a>
          <a href="#" class="flex items-center space-x-3 bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.5 14.5L12 13l-4.5 3.5V7h9v9.5z"/>
            </svg>
            <span>Podcast Addict</span>
          </a>
        </div>

        <!-- RSS Feed -->
        <div class="text-center">
          <p class="text-sm text-gray-500 mb-2">Or subscribe via RSS feed:</p>
          <a href="#" class="text-african-red hover:text-african-gold font-medium text-sm">
            https://feeds.pjmafrica.com/podcast
          </a>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout>
