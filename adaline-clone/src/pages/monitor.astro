---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="Monitor | Adaline">
  <Header />
  <main class="pt-16">
    <!-- Hero Section -->
    <section class="py-20 bg-gradient-to-br from-orange-50 to-red-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center max-w-4xl mx-auto">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Monitor
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            Monitor your AI applications in real-time with comprehensive analytics, performance insights, and intelligent alerting to ensure optimal performance.
          </p>
          <a href="https://app.adaline.ai" 
             class="inline-flex items-center bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors font-medium">
            Start Monitoring
            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div class="w-full h-96 bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl flex items-center justify-center">
            <span class="text-gray-600">Monitoring Dashboard Preview</span>
          </div>
          <div>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Real-time Analytics & Insights
            </h2>
            <p class="text-lg text-gray-600 mb-8">
              Get deep insights into your AI application performance with real-time monitoring, detailed analytics, and proactive alerting to maintain optimal user experience.
            </p>
            <ul class="space-y-4">
              <li class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-orange-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Real-time performance metrics</span>
              </li>
              <li class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-orange-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Intelligent alerting system</span>
              </li>
              <li class="flex items-start space-x-3">
                <svg class="w-6 h-6 text-orange-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">Detailed usage analytics</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </main>
  <Footer />
</Layout>
