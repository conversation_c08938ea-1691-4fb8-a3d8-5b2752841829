---
// Header component
---

<header class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm">
  <nav class="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8">
    <div class="flex justify-between items-center h-14 sm:h-16">
      <!-- Logo -->
      <div class="flex items-center flex-shrink-0">
        <a href="/" class="flex items-center space-x-2 sm:space-x-3">
          <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-african-red to-african-gold rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xs sm:text-sm">PJM</span>
          </div>
          <div class="flex flex-col">
            <span class="text-lg sm:text-xl font-bold text-gray-900 leading-tight">PJM Africa</span>
            <span class="text-xs text-gray-600 -mt-0.5 hidden sm:block">Yourself, Redefined</span>
          </div>
        </a>
      </div>

      <!-- Navigation Links -->
      <div class="hidden lg:flex items-center space-x-8">
        <div class="flex items-center space-x-6">
          <a href="/about" class="text-gray-600 hover:text-african-red transition-colors font-medium">About</a>
          <a href="/watch" class="text-gray-600 hover:text-african-red transition-colors font-medium">Watch</a>
          <a href="/listen" class="text-gray-600 hover:text-african-red transition-colors font-medium">Listen</a>
          <a href="/blog" class="text-gray-600 hover:text-african-red transition-colors font-medium">Read</a>
          <a href="/advisory" class="text-gray-600 hover:text-african-red transition-colors font-medium">Advisory</a>
          <a href="/join" class="text-gray-600 hover:text-african-red transition-colors font-medium">Join</a>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex items-center space-x-2 sm:space-x-4">
        <a href="/watch" class="hidden lg:flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-african-red transition-colors">
          <span class="text-sm font-medium">Watch</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 616 0v2M7 16a3 3 0 006 0v-2"></path>
          </svg>
        </a>
        <a href="/join" class="bg-gradient-to-r from-african-red to-african-gold text-white px-3 py-2 sm:px-4 sm:py-2 rounded-lg hover:from-african-gold hover:to-african-red transition-all text-sm sm:text-base font-medium">
          <span class="hidden sm:inline">Join the Movement</span>
          <span class="sm:hidden">Join</span>
        </a>

        <!-- Mobile menu button -->
        <button
          class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-african-red focus:ring-offset-2"
          id="mobile-menu-button"
          aria-label="Toggle mobile menu"
          aria-expanded="false"
        >
          <div class="hamburger-icon">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </div>
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <div class="lg:hidden mobile-menu" id="mobile-menu">
      <div class="mobile-menu-content">
        <div class="px-4 py-6 space-y-4 bg-white/95 backdrop-blur-md border-t border-gray-200 shadow-lg">
          <a href="/about" class="mobile-menu-link">
            <span>About</span>
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
          <a href="/watch" class="mobile-menu-link">
            <span>Watch</span>
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
          <a href="/listen" class="mobile-menu-link">
            <span>Listen</span>
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
          <a href="/blog" class="mobile-menu-link">
            <span>Read</span>
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
          <a href="/advisory" class="mobile-menu-link">
            <span>Advisory</span>
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
          <a href="/join" class="mobile-menu-link">
            <span>Join</span>
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>

          <!-- Mobile CTA -->
          <div class="pt-4 border-t border-gray-200">
            <a href="/join" class="block w-full bg-gradient-to-r from-african-red to-african-gold text-white text-center px-4 py-3 rounded-lg hover:from-african-gold hover:to-african-red transition-all font-medium">
              Join the Movement
            </a>
          </div>
        </div>
      </div>
    </div>
  </nav>
</header>

<style>
  /* Hamburger menu animation */
  .hamburger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .hamburger-line {
    width: 100%;
    height: 2px;
    background-color: #374151;
    border-radius: 1px;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  .hamburger-icon.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
  }

  .hamburger-icon.active .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .hamburger-icon.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
  }

  /* Mobile menu animations */
  .mobile-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
  }

  .mobile-menu.active {
    max-height: 500px;
    transition: max-height 0.3s ease-in;
  }

  .mobile-menu-content {
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
  }

  .mobile-menu.active .mobile-menu-content {
    transform: translateY(0);
    opacity: 1;
    transition-delay: 0.1s;
  }

  /* Mobile menu links */
  .mobile-menu-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    color: #374151;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    min-height: 48px; /* Touch-friendly minimum height */
  }

  .mobile-menu-link:hover {
    background-color: #fef2f2;
    color: #dc2626;
  }

  .mobile-menu-link:active {
    background-color: #fee2e2;
    transform: scale(0.98);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .mobile-menu-link {
      padding: 16px;
      font-size: 16px;
    }
  }

  /* Focus styles for accessibility */
  .mobile-menu-link:focus {
    outline: 2px solid #dc2626;
    outline-offset: 2px;
  }

  /* Backdrop blur support */
  @supports (backdrop-filter: blur(12px)) {
    .mobile-menu-content {
      backdrop-filter: blur(12px);
    }
  }
</style>

<script>
  // Mobile menu toggle with enhanced functionality
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  const hamburgerIcon = mobileMenuButton?.querySelector('.hamburger-icon');
  let isMenuOpen = false;

  function toggleMobileMenu() {
    isMenuOpen = !isMenuOpen;

    // Toggle menu visibility
    mobileMenu?.classList.toggle('active');

    // Toggle hamburger animation
    hamburgerIcon?.classList.toggle('active');

    // Update aria-expanded for accessibility
    mobileMenuButton?.setAttribute('aria-expanded', isMenuOpen.toString());

    // Prevent body scroll when menu is open
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  // Add click event listener
  mobileMenuButton?.addEventListener('click', toggleMobileMenu);

  // Close menu when clicking on a link
  const mobileMenuLinks = mobileMenu?.querySelectorAll('.mobile-menu-link');
  mobileMenuLinks?.forEach(link => {
    link.addEventListener('click', () => {
      if (isMenuOpen) {
        toggleMobileMenu();
      }
    });
  });

  // Close menu when clicking outside
  document.addEventListener('click', (event) => {
    const target = event.target as Node;
    if (isMenuOpen &&
        !mobileMenu?.contains(target) &&
        !mobileMenuButton?.contains(target)) {
      toggleMobileMenu();
    }
  });

  // Close menu on escape key
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && isMenuOpen) {
      toggleMobileMenu();
    }
  });

  // Handle window resize
  window.addEventListener('resize', () => {
    if (window.innerWidth >= 1024 && isMenuOpen) {
      toggleMobileMenu();
    }
  });
</script>
