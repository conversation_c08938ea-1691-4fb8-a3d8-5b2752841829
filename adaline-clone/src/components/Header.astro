---
// Header component
---

<header class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
  <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-2">
          <div class="w-10 h-10 bg-gradient-to-r from-african-red to-african-gold rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">PJM</span>
          </div>
          <div class="flex flex-col">
            <span class="text-xl font-bold text-gray-900">PJM Africa</span>
            <span class="text-xs text-gray-600 -mt-1">Yourself, Redefined</span>
          </div>
        </a>
      </div>

      <!-- Navigation Links -->
      <div class="hidden md:flex items-center space-x-8">
        <div class="flex items-center space-x-6">
          <a href="/about" class="text-gray-600 hover:text-african-red transition-colors">About</a>
          <a href="/watch" class="text-gray-600 hover:text-african-red transition-colors">Watch</a>
          <a href="/listen" class="text-gray-600 hover:text-african-red transition-colors">Listen</a>
          <a href="/blog" class="text-gray-600 hover:text-african-red transition-colors">Read</a>
          <a href="/advisory" class="text-gray-600 hover:text-african-red transition-colors">Advisory</a>
          <a href="/join" class="text-gray-600 hover:text-african-red transition-colors">Join</a>
        </div>
      </div>

      <!-- CTA Buttons -->
      <div class="flex items-center space-x-4">
        <a href="/watch" class="hidden md:flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-african-red transition-colors">
          <span>Watch</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 616 0v2M7 16a3 3 0 006 0v-2"></path>
          </svg>
        </a>
        <a href="/join" class="bg-gradient-to-r from-african-red to-african-gold text-white px-4 py-2 rounded-lg hover:from-african-gold hover:to-african-red transition-all">
          Join the Movement
        </a>
        
        <!-- Mobile menu button -->
        <button class="md:hidden p-2" id="mobile-menu-button">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <div class="md:hidden hidden" id="mobile-menu">
      <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
        <a href="/about" class="block px-3 py-2 text-gray-600 hover:text-african-red">About</a>
        <a href="/watch" class="block px-3 py-2 text-gray-600 hover:text-african-red">Watch</a>
        <a href="/listen" class="block px-3 py-2 text-gray-600 hover:text-african-red">Listen</a>
        <a href="/blog" class="block px-3 py-2 text-gray-600 hover:text-african-red">Read</a>
        <a href="/advisory" class="block px-3 py-2 text-gray-600 hover:text-african-red">Advisory</a>
        <a href="/join" class="block px-3 py-2 text-gray-600 hover:text-african-red">Join</a>
      </div>
    </div>
  </nav>
</header>

<script>
  // Mobile menu toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  mobileMenuButton?.addEventListener('click', () => {
    mobileMenu?.classList.toggle('hidden');
  });
</script>
