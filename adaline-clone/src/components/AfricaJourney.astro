---
// Africa Journey section component
---

<section class="py-20 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Journey Through Africa
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Explore the continent through five distinct regions, each with unique stories, cultures, and perspectives that shape the African narrative.
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Left side - Region Tabs -->
      <div>
        <div class="space-y-4" id="region-navigation">
          <!-- West Africa Tab -->
          <div class="region-item active" data-region="west">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-african-red bg-african-red/5 cursor-pointer transition-all duration-200 hover:border-african-red/80">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-african-red rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🌍</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">West Africa</h3>
                    <span class="text-sm text-african-red font-medium">8 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Ghana, Nigeria, Senegal, Mali, Burkina Faso, Ivory Coast, Guinea, Sierra Leone</p>
                </div>
              </div>
            </div>
          </div>

          <!-- East Africa Tab -->
          <div class="region-item" data-region="east">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🏔️</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">East Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">7 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Kenya, Tanzania, Uganda, Rwanda, Ethiopia, Djibouti, Somalia</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Southern Africa Tab -->
          <div class="region-item" data-region="south">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🦁</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">Southern Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">6 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">South Africa, Zimbabwe, Botswana, Namibia, Zambia, Lesotho</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Central Africa Tab -->
          <div class="region-item" data-region="central">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🌳</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">Central Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">5 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Cameroon, Chad, Central African Republic, Congo, Gabon</p>
                </div>
              </div>
            </div>
          </div>

          <!-- North Africa Tab -->
          <div class="region-item" data-region="north">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🏜️</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">North Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">6 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Egypt, Libya, Tunisia, Algeria, Morocco, Sudan</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Learn More Links -->
        <div class="pt-6">
          <p class="text-sm text-gray-500 mb-3">Explore more</p>
          <div class="flex flex-wrap gap-4">
            <a href="/watch" class="text-african-red hover:text-african-gold text-sm font-medium">Watch Episodes</a>
            <a href="/listen" class="text-african-red hover:text-african-gold text-sm font-medium">Listen to Podcasts</a>
            <a href="/read" class="text-african-red hover:text-african-gold text-sm font-medium">Read Stories</a>
          </div>
        </div>
      </div>

      <!-- Right side - Region Content -->
      <div class="relative">
        <!-- West Africa Content -->
        <div class="region-content active" id="west-content">
          <div class="bg-gradient-to-br from-african-red/10 to-african-gold/10 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">West Africa: The Cultural Heartbeat</h3>
            <p class="text-gray-600 mb-6">
              From the bustling markets of Lagos to the historic shores of Ghana, West Africa pulses with rhythm, tradition, and innovation. Discover the stories that shaped a continent.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-african-red">8</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-african-gold">12</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Ghana's Independence Legacy, Nigeria's Tech Revolution</div>
            </div>
          </div>
        </div>

        <!-- Other region contents (hidden by default) -->
        <div class="region-content hidden" id="east-content">
          <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">East Africa: Cradle of Humanity</h3>
            <p class="text-gray-600 mb-6">
              Where humanity began, East Africa continues to lead with innovation, conservation, and cultural preservation. Experience the birthplace of civilization.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">7</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">10</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Kenya's Wildlife Conservation, Rwanda's Remarkable Recovery</div>
            </div>
          </div>
        </div>

        <div class="region-content hidden" id="south-content">
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Southern Africa: Land of Resilience</h3>
            <p class="text-gray-600 mb-6">
              From the struggle against apartheid to the diamonds of Botswana, Southern Africa tells stories of resilience, transformation, and hope.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600">6</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-pink-600">8</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: South Africa's Rainbow Nation, Botswana's Diamond Success</div>
            </div>
          </div>
        </div>

        <div class="region-content hidden" id="central-content">
          <div class="bg-gradient-to-br from-green-50 to-yellow-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Central Africa: Heart of the Continent</h3>
            <p class="text-gray-600 mb-6">
              Dense rainforests, rich resources, and diverse cultures define Central Africa. Explore the continent's beating heart and its untold stories.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">5</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-yellow-600">6</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Cameroon's Cultural Diversity, Gabon's Conservation Efforts</div>
            </div>
          </div>
        </div>

        <div class="region-content hidden" id="north-content">
          <div class="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">North Africa: Ancient Meets Modern</h3>
            <p class="text-gray-600 mb-6">
              Where pharaohs once ruled and trade routes flourished, North Africa bridges Africa with the Mediterranean, blending ancient wisdom with modern ambition.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-yellow-600">6</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-orange-600">9</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Egypt's Timeless Legacy, Morocco's Cultural Crossroads</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Region switching functionality
  document.addEventListener('DOMContentLoaded', function() {
    const regionItems = document.querySelectorAll('.region-item');
    const regionContents = document.querySelectorAll('.region-content');

    regionItems.forEach(item => {
      item.addEventListener('click', function() {
        const regionName = this.dataset.region;
        
        // Remove active class from all regions
        regionItems.forEach(region => {
          region.classList.remove('active');
          const regionDiv = region.querySelector('div');
          regionDiv.classList.remove('border-african-red', 'bg-african-red/5');
          regionDiv.classList.add('border-gray-200', 'bg-gray-50');
          
          const icon = region.querySelector('.w-12');
          icon.classList.remove('bg-african-red');
          icon.classList.add('bg-gray-400');
          
          const number = region.querySelector('span');
          number.classList.remove('text-african-red');
          number.classList.add('text-gray-500');
        });
        
        // Add active class to clicked region
        this.classList.add('active');
        const activeRegionDiv = this.querySelector('div');
        activeRegionDiv.classList.remove('border-gray-200', 'bg-gray-50');
        activeRegionDiv.classList.add('border-african-red', 'bg-african-red/5');
        
        const activeIcon = this.querySelector('.w-12');
        activeIcon.classList.remove('bg-gray-400');
        activeIcon.classList.add('bg-african-red');
        
        const activeNumber = this.querySelector('span');
        activeNumber.classList.remove('text-gray-500');
        activeNumber.classList.add('text-african-red');
        
        // Hide all region contents
        regionContents.forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });
        
        // Show selected region content
        const activeContent = document.getElementById(regionName + '-content');
        if (activeContent) {
          activeContent.classList.remove('hidden');
          activeContent.classList.add('active');
        }
      });
    });
  });
</script>
