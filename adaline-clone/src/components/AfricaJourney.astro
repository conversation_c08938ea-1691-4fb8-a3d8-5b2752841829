---
// Africa Journey section component
---

<section class="py-20 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Journey Through Africa
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Explore the continent through five distinct regions, each with unique stories, cultures, and perspectives that shape the African narrative.
      </p>
    </div>

    <!-- Mobile Layout: Accordion Style -->
    <div class="lg:hidden">
      <!-- Mobile Region Accordion -->
      <div class="space-y-3" id="mobile-region-accordion">
        <!-- West Africa Accordion Item -->
        <div class="mobile-accordion-item" data-region="west">
          <!-- Tab Header -->
          <div class="accordion-header flex items-center justify-between p-4 rounded-xl border-2 border-african-red bg-african-red/5 cursor-pointer transition-all duration-200 hover:border-african-red/80">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-african-red rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">🌍</span>
              </div>
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-base font-semibold text-gray-900">West Africa</h3>
                  <span class="text-xs text-african-red font-medium">8 Countries</span>
                </div>
                <p class="text-xs text-gray-600 mt-1">Ghana, Nigeria, Senegal, Mali...</p>
              </div>
            </div>
            <!-- Active indicator -->
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-african-red rounded-full active-indicator"></div>
              <svg class="w-5 h-5 text-african-red transform transition-transform duration-300 chevron-icon rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
          <!-- Accordion Content -->
          <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" style="max-height: 300px;">
            <div class="accordion-content-inner p-4 pt-0">
              <!-- Content will be dynamically inserted here -->
            </div>
          </div>
        </div>

        <!-- East Africa Accordion Item -->
        <div class="mobile-accordion-item" data-region="east">
          <!-- Tab Header -->
          <div class="accordion-header flex items-center justify-between p-4 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">🏔️</span>
              </div>
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-base font-semibold text-gray-900">East Africa</h3>
                  <span class="text-xs text-gray-500 font-medium">7 Countries</span>
                </div>
                <p class="text-xs text-gray-600 mt-1">Kenya, Tanzania, Uganda, Rwanda...</p>
              </div>
            </div>
            <!-- Inactive indicator -->
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-gray-300 rounded-full active-indicator"></div>
              <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-300 chevron-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
          <!-- Accordion Content -->
          <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" style="max-height: 0;">
            <div class="accordion-content-inner p-4 pt-0">
              <!-- Content will be dynamically inserted here -->
            </div>
          </div>
        </div>

        <!-- Southern Africa Accordion Item -->
        <div class="mobile-accordion-item" data-region="south">
          <!-- Tab Header -->
          <div class="accordion-header flex items-center justify-between p-4 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">🦁</span>
              </div>
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-base font-semibold text-gray-900">Southern Africa</h3>
                  <span class="text-xs text-gray-500 font-medium">6 Countries</span>
                </div>
                <p class="text-xs text-gray-600 mt-1">South Africa, Zimbabwe, Botswana...</p>
              </div>
            </div>
            <!-- Inactive indicator -->
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-gray-300 rounded-full active-indicator"></div>
              <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-300 chevron-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
          <!-- Accordion Content -->
          <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" style="max-height: 0;">
            <div class="accordion-content-inner p-4 pt-0">
              <!-- Content will be dynamically inserted here -->
            </div>
          </div>
        </div>

        <!-- Central Africa Accordion Item -->
        <div class="mobile-accordion-item" data-region="central">
          <!-- Tab Header -->
          <div class="accordion-header flex items-center justify-between p-4 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">🌳</span>
              </div>
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-base font-semibold text-gray-900">Central Africa</h3>
                  <span class="text-xs text-gray-500 font-medium">5 Countries</span>
                </div>
                <p class="text-xs text-gray-600 mt-1">Cameroon, Chad, CAR, Congo...</p>
              </div>
            </div>
            <!-- Inactive indicator -->
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-gray-300 rounded-full active-indicator"></div>
              <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-300 chevron-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
          <!-- Accordion Content -->
          <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" style="max-height: 0;">
            <div class="accordion-content-inner p-4 pt-0">
              <!-- Content will be dynamically inserted here -->
            </div>
          </div>
        </div>

        <!-- North Africa Accordion Item -->
        <div class="mobile-accordion-item" data-region="north">
          <!-- Tab Header -->
          <div class="accordion-header flex items-center justify-between p-4 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-400 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">🏜️</span>
              </div>
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <h3 class="text-base font-semibold text-gray-900">North Africa</h3>
                  <span class="text-xs text-gray-500 font-medium">6 Countries</span>
                </div>
                <p class="text-xs text-gray-600 mt-1">Egypt, Libya, Tunisia, Algeria...</p>
              </div>
            </div>
            <!-- Inactive indicator -->
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-gray-300 rounded-full active-indicator"></div>
              <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-300 chevron-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
          <!-- Accordion Content -->
          <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" style="max-height: 0;">
            <div class="accordion-content-inner p-4 pt-0">
              <!-- Content will be dynamically inserted here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile Learn More Links -->
      <div class="pt-6">
        <p class="text-sm text-gray-500 mb-3">Explore more</p>
        <div class="flex flex-wrap gap-4">
          <a href="/watch" class="text-african-red hover:text-african-gold text-sm font-medium">Watch Episodes</a>
          <a href="/listen" class="text-african-red hover:text-african-gold text-sm font-medium">Listen to Podcasts</a>
          <a href="/read" class="text-african-red hover:text-african-gold text-sm font-medium">Read Stories</a>
        </div>
      </div>
    </div>

    <!-- Desktop Layout: Side by side -->
    <div class="hidden lg:grid lg:grid-cols-2 gap-12 items-center">
      <!-- Left side - Region Tabs -->
      <div>
        <div class="space-y-4" id="region-navigation">
          <!-- West Africa Tab -->
          <div class="region-item active" data-region="west">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-african-red bg-african-red/5 cursor-pointer transition-all duration-200 hover:border-african-red/80">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-african-red rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🌍</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">West Africa</h3>
                    <span class="text-sm text-african-red font-medium">8 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Ghana, Nigeria, Senegal, Mali, Burkina Faso, Ivory Coast, Guinea, Sierra Leone</p>
                </div>
              </div>
            </div>
          </div>

          <!-- East Africa Tab -->
          <div class="region-item" data-region="east">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🏔️</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">East Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">7 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Kenya, Tanzania, Uganda, Rwanda, Ethiopia, Djibouti, Somalia</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Southern Africa Tab -->
          <div class="region-item" data-region="south">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🦁</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">Southern Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">6 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">South Africa, Zimbabwe, Botswana, Namibia, Zambia, Lesotho</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Central Africa Tab -->
          <div class="region-item" data-region="central">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🌳</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">Central Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">5 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Cameroon, Chad, Central African Republic, Congo, Gabon</p>
                </div>
              </div>
            </div>
          </div>

          <!-- North Africa Tab -->
          <div class="region-item" data-region="north">
            <div class="flex items-center space-x-4 p-6 rounded-xl border-2 border-gray-200 bg-gray-50 cursor-pointer transition-all duration-200 hover:border-gray-300">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                  <span class="text-white font-bold text-sm">🏜️</span>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-900">North Africa</h3>
                    <span class="text-sm text-gray-500 font-medium">6 Countries</span>
                  </div>
                  <p class="text-sm text-gray-600">Egypt, Libya, Tunisia, Algeria, Morocco, Sudan</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Learn More Links -->
        <div class="pt-6">
          <p class="text-sm text-gray-500 mb-3">Explore more</p>
          <div class="flex flex-wrap gap-4">
            <a href="/watch" class="text-african-red hover:text-african-gold text-sm font-medium">Watch Episodes</a>
            <a href="/listen" class="text-african-red hover:text-african-gold text-sm font-medium">Listen to Podcasts</a>
            <a href="/read" class="text-african-red hover:text-african-gold text-sm font-medium">Read Stories</a>
          </div>
        </div>
      </div>

      <!-- Right side - Region Content -->
      <div class="relative">
        <!-- West Africa Content -->
        <div class="region-content active" id="west-content">
          <div class="bg-gradient-to-br from-african-red/10 to-african-gold/10 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">West Africa: The Cultural Heartbeat</h3>
            <p class="text-gray-600 mb-6">
              From the bustling markets of Lagos to the historic shores of Ghana, West Africa pulses with rhythm, tradition, and innovation. Discover the stories that shaped a continent.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-african-red">8</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-african-gold">12</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Ghana's Independence Legacy, Nigeria's Tech Revolution</div>
            </div>
          </div>
        </div>

        <!-- Other region contents (hidden by default) -->
        <div class="region-content hidden" id="east-content">
          <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">East Africa: Cradle of Humanity</h3>
            <p class="text-gray-600 mb-6">
              Where humanity began, East Africa continues to lead with innovation, conservation, and cultural preservation. Experience the birthplace of civilization.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">7</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">10</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Kenya's Wildlife Conservation, Rwanda's Remarkable Recovery</div>
            </div>
          </div>
        </div>

        <div class="region-content hidden" id="south-content">
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Southern Africa: Land of Resilience</h3>
            <p class="text-gray-600 mb-6">
              From the struggle against apartheid to the diamonds of Botswana, Southern Africa tells stories of resilience, transformation, and hope.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-purple-600">6</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-pink-600">8</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: South Africa's Rainbow Nation, Botswana's Diamond Success</div>
            </div>
          </div>
        </div>

        <div class="region-content hidden" id="central-content">
          <div class="bg-gradient-to-br from-green-50 to-yellow-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Central Africa: Heart of the Continent</h3>
            <p class="text-gray-600 mb-6">
              Dense rainforests, rich resources, and diverse cultures define Central Africa. Explore the continent's beating heart and its untold stories.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">5</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-yellow-600">6</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Cameroon's Cultural Diversity, Gabon's Conservation Efforts</div>
            </div>
          </div>
        </div>

        <div class="region-content hidden" id="north-content">
          <div class="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">North Africa: Ancient Meets Modern</h3>
            <p class="text-gray-600 mb-6">
              Where pharaohs once ruled and trade routes flourished, North Africa bridges Africa with the Mediterranean, blending ancient wisdom with modern ambition.
            </p>
            <div class="bg-white rounded-xl p-6 shadow-sm">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-yellow-600">6</div>
                  <div class="text-sm text-gray-600">Countries Visited</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-orange-600">9</div>
                  <div class="text-sm text-gray-600">Episodes Filmed</div>
                </div>
              </div>
              <div class="text-sm text-gray-500">Featured: Egypt's Timeless Legacy, Morocco's Cultural Crossroads</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  /* Accordion animations */
  .accordion-content {
    transition: max-height 0.3s ease-in-out, opacity 0.2s ease-in-out;
  }

  .accordion-content-inner {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
  }

  .mobile-accordion-item.active .accordion-content-inner {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.1s;
  }

  /* Active indicator animations */
  .active-indicator {
    transition: all 0.2s ease;
  }

  .chevron-icon {
    transition: all 0.3s ease;
  }

  .mobile-accordion-item.active .chevron-icon {
    transform: rotate(90deg);
  }

  /* Smooth accordion content animation */
  @keyframes accordionSlideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .accordion-content.expanding .accordion-content-inner {
    animation: accordionSlideIn 0.3s ease-out;
  }
</style>

<script>
  // Region content data
  const regionContentData = {
    west: {
      title: "West Africa: The Cultural Heartbeat",
      description: "From the bustling markets of Lagos to the historic shores of Ghana, West Africa pulses with rhythm, tradition, and innovation. Discover the stories that shaped a continent.",
      countries: 8,
      episodes: 12,
      featured: "Ghana's Independence Legacy, Nigeria's Tech Revolution",
      gradient: "from-african-red/10 to-african-gold/10",
      countryColor: "text-african-red",
      episodeColor: "text-african-gold"
    },
    east: {
      title: "East Africa: Cradle of Humanity",
      description: "Where humanity began, East Africa continues to lead with innovation, conservation, and cultural preservation. Experience the birthplace of civilization.",
      countries: 7,
      episodes: 10,
      featured: "Kenya's Wildlife Conservation, Rwanda's Remarkable Recovery",
      gradient: "from-green-50 to-blue-50",
      countryColor: "text-green-600",
      episodeColor: "text-blue-600"
    },
    south: {
      title: "Southern Africa: Land of Resilience",
      description: "From the struggle against apartheid to the diamonds of Botswana, Southern Africa tells stories of resilience, transformation, and hope.",
      countries: 6,
      episodes: 8,
      featured: "South Africa's Rainbow Nation, Botswana's Diamond Success",
      gradient: "from-purple-50 to-pink-50",
      countryColor: "text-purple-600",
      episodeColor: "text-pink-600"
    },
    central: {
      title: "Central Africa: Heart of the Continent",
      description: "Dense rainforests, rich resources, and diverse cultures define Central Africa. Explore the continent's beating heart and its untold stories.",
      countries: 5,
      episodes: 6,
      featured: "Cameroon's Cultural Diversity, Gabon's Conservation Efforts",
      gradient: "from-green-50 to-yellow-50",
      countryColor: "text-green-600",
      episodeColor: "text-yellow-600"
    },
    north: {
      title: "North Africa: Ancient Meets Modern",
      description: "Where pharaohs once ruled and trade routes flourished, North Africa bridges Africa with the Mediterranean, blending ancient wisdom with modern ambition.",
      countries: 6,
      episodes: 9,
      featured: "Egypt's Timeless Legacy, Morocco's Cultural Crossroads",
      gradient: "from-yellow-50 to-orange-50",
      countryColor: "text-yellow-600",
      episodeColor: "text-orange-600"
    }
  };

  // Accordion functionality for mobile and region switching for desktop
  document.addEventListener('DOMContentLoaded', function() {
    const regionItems = document.querySelectorAll('.region-item');
    const regionContents = document.querySelectorAll('.region-content');
    const mobileAccordionItems = document.querySelectorAll('.mobile-accordion-item');

    // Function to create accordion content
    function createAccordionContent(regionName) {
      const data = regionContentData[regionName];
      if (!data) return '';

      return `
        <div class="bg-gradient-to-br ${data.gradient} rounded-xl p-4 mt-3">
          <h4 class="text-lg font-bold text-gray-900 mb-2">${data.title}</h4>
          <p class="text-gray-600 mb-3 text-sm">
            ${data.description}
          </p>
          <div class="bg-white rounded-lg p-3 shadow-sm">
            <div class="grid grid-cols-2 gap-3 mb-2">
              <div class="text-center">
                <div class="text-lg font-bold ${data.countryColor}">${data.countries}</div>
                <div class="text-xs text-gray-600">Countries</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-bold ${data.episodeColor}">${data.episodes}</div>
                <div class="text-xs text-gray-600">Episodes</div>
              </div>
            </div>
            <div class="text-xs text-gray-500">Featured: ${data.featured}</div>
          </div>
        </div>
      `;
    }

    // Function to update visual indicators for both mobile and desktop
    function updateVisualIndicators(activeRegion, isMobile = false) {
      const items = isMobile ? mobileAccordionItems : regionItems;

      items.forEach(item => {
        const isActive = item.dataset.region === activeRegion;
        const header = isMobile ? item.querySelector('.accordion-header') : item.querySelector('div');
        const icon = item.querySelector('.w-10, .w-12');
        const number = item.querySelector('span');
        const indicator = item.querySelector('.active-indicator');
        const chevron = item.querySelector('.chevron-icon');

        // Reset all items
        item.classList.remove('active');
        header?.classList.remove('border-african-red', 'bg-african-red/5');
        header?.classList.add('border-gray-200', 'bg-gray-50');
        icon?.classList.remove('bg-african-red');
        icon?.classList.add('bg-gray-400');
        number?.classList.remove('text-african-red');
        number?.classList.add('text-gray-500');
        indicator?.classList.remove('bg-african-red');
        indicator?.classList.add('bg-gray-300');
        chevron?.classList.remove('text-african-red', 'rotate-90');
        chevron?.classList.add('text-gray-400');

        // Activate selected item
        if (isActive) {
          item.classList.add('active');
          header?.classList.remove('border-gray-200', 'bg-gray-50');
          header?.classList.add('border-african-red', 'bg-african-red/5');
          icon?.classList.remove('bg-gray-400');
          icon?.classList.add('bg-african-red');
          number?.classList.remove('text-gray-500');
          number?.classList.add('text-african-red');
          indicator?.classList.remove('bg-gray-300');
          indicator?.classList.add('bg-african-red');
          chevron?.classList.remove('text-gray-400');
          chevron?.classList.add('text-african-red', 'rotate-90');
        }
      });
    }

    // Function to expand/collapse accordion
    function toggleAccordion(targetItem, regionName) {
      const accordionContent = targetItem.querySelector('.accordion-content');
      const contentInner = targetItem.querySelector('.accordion-content-inner');
      const isCurrentlyActive = targetItem.classList.contains('active');

      // Collapse all accordion items first
      mobileAccordionItems.forEach(item => {
        const content = item.querySelector('.accordion-content');
        const inner = item.querySelector('.accordion-content-inner');

        item.classList.remove('active');
        content.style.maxHeight = '0';
        inner.style.opacity = '0';
        inner.style.transform = 'translateY(-10px)';
      });

      // If clicking on a different item or the same item when collapsed, expand it
      if (!isCurrentlyActive) {
        targetItem.classList.add('active');

        // Insert content
        contentInner.innerHTML = createAccordionContent(regionName);

        // Calculate and set max height for smooth animation
        const contentHeight = contentInner.scrollHeight + 32; // Add padding
        accordionContent.style.maxHeight = contentHeight + 'px';

        // Animate content appearance
        setTimeout(() => {
          contentInner.style.opacity = '1';
          contentInner.style.transform = 'translateY(0)';
        }, 100);
      }
    }

    // Initialize with West Africa active on mobile
    if (window.innerWidth < 1024) {
      updateVisualIndicators('west', true);
      const westAccordion = document.querySelector('.mobile-accordion-item[data-region="west"]');
      if (westAccordion) {
        toggleAccordion(westAccordion, 'west');
      }
    } else {
      updateVisualIndicators('west', false);
    }

    // Mobile accordion click handlers
    mobileAccordionItems.forEach(item => {
      const header = item.querySelector('.accordion-header');
      header.addEventListener('click', function() {
        const regionName = item.dataset.region;

        // Update visual indicators
        updateVisualIndicators(regionName, true);

        // Toggle accordion
        toggleAccordion(item, regionName);
      });
    });

    // Desktop region click handlers
    regionItems.forEach(item => {
      item.addEventListener('click', function() {
        const regionName = this.dataset.region;

        // Update visual indicators for desktop
        updateVisualIndicators(regionName, false);

        // Update desktop content
        regionContents.forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });

        const activeContent = document.getElementById(regionName + '-content');
        if (activeContent) {
          activeContent.classList.remove('hidden');
          activeContent.classList.add('active');
        }
      });
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 1024) {
        // Reset mobile accordion when switching to desktop
        mobileAccordionItems.forEach(item => {
          const content = item.querySelector('.accordion-content');
          content.style.maxHeight = '0';
          item.classList.remove('active');
        });
      }
    });
  });
</script>
