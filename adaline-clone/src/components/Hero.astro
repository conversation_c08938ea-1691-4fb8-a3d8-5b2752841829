---
// Interactive 3D Hero Component with Scroll Animations
---

<section class="hero-3d relative min-h-screen overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black">
  <!-- 3D Scene Container -->
  <div class="scene-container relative w-full h-screen perspective-1000">

    <!-- Background Layers with Parallax -->
    <div class="parallax-layer layer-far absolute inset-0 transform-gpu">
      <!-- African Sky/Mountains Background -->
      <div class="absolute inset-0 bg-gradient-to-b from-orange-400 via-red-500 to-purple-900 opacity-80"></div>
      <div class="absolute inset-0 mountain-silhouette bg-cover bg-bottom"></div>
    </div>

    <!-- Mid Layer - African Landscapes -->
    <div class="parallax-layer layer-mid absolute inset-0 transform-gpu">
      <!-- Savanna Silhouettes -->
      <div class="landscape-element savanna absolute bottom-0 left-0 w-full h-2/3 opacity-70">
        <div class="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-black/60 to-transparent"></div>
        <!-- Acacia Trees -->
        <div class="tree-silhouette absolute bottom-20 left-1/4 w-16 h-32 bg-black/80 rounded-full transform rotate-12"></div>
        <div class="tree-silhouette absolute bottom-16 right-1/3 w-12 h-28 bg-black/70 rounded-full transform -rotate-6"></div>
        <div class="tree-silhouette absolute bottom-24 right-1/4 w-20 h-36 bg-black/90 rounded-full transform rotate-3"></div>
      </div>

      <!-- City Skylines -->
      <div class="cityscape absolute bottom-0 right-0 w-2/3 h-1/2 opacity-60">
        <!-- Building Silhouettes -->
        <div class="building absolute bottom-0 right-1/4 w-8 h-24 bg-gradient-to-t from-gray-900 to-gray-700"></div>
        <div class="building absolute bottom-0 right-1/3 w-12 h-32 bg-gradient-to-t from-gray-800 to-gray-600"></div>
        <div class="building absolute bottom-0 right-1/5 w-6 h-20 bg-gradient-to-t from-gray-900 to-gray-700"></div>
        <div class="building absolute bottom-0 right-1/6 w-10 h-28 bg-gradient-to-t from-gray-800 to-gray-600"></div>
        <!-- Building Lights -->
        <div class="light absolute bottom-8 right-1/4 w-1 h-1 bg-yellow-400 rounded-full animate-pulse"></div>
        <div class="light absolute bottom-12 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-300"></div>
        <div class="light absolute bottom-6 right-1/5 w-1 h-1 bg-white rounded-full animate-pulse delay-700"></div>
      </div>
    </div>

    <!-- Foreground Layer - Cultural Elements -->
    <div class="parallax-layer layer-near absolute inset-0 transform-gpu">
      <!-- Floating Cultural Icons -->
      <div class="cultural-element mask absolute top-1/4 left-1/6 w-16 h-16 opacity-40 animate-float">
        <div class="w-full h-full bg-african-gold rounded-full flex items-center justify-center text-2xl">🎭</div>
      </div>
      <div class="cultural-element drum absolute top-1/3 right-1/5 w-12 h-12 opacity-30 animate-float-delayed">
        <div class="w-full h-full bg-african-red rounded-full flex items-center justify-center text-xl">🥁</div>
      </div>
      <div class="cultural-element art absolute bottom-1/3 left-1/8 w-14 h-14 opacity-35 animate-float-slow">
        <div class="w-full h-full bg-orange-500 rounded-full flex items-center justify-center text-xl">🎨</div>
      </div>
    </div>

    <!-- Announcement Banner -->
    <div class="absolute top-20 left-1/2 transform -translate-x-1/2 z-20">
      <a href="/about"
         class="inline-flex items-center space-x-2 bg-black/30 backdrop-blur-md border border-african-red/30 rounded-full px-6 py-3 text-sm font-medium text-white hover:bg-black/50 transition-all duration-200">
        <span class="text-lg">🌍</span>
        <span>32 Countries. 36 Weeks. One Mission: Africa United.</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </a>
    </div>

    <!-- Main Content Layer -->
    <div class="content-layer absolute inset-0 flex items-center justify-center z-10">
      <div class="hero-content text-center max-w-4xl mx-auto px-6 transform-gpu">
        <!-- Main Title with 3D Effect -->
        <h1 class="hero-title text-6xl md:text-8xl font-bold text-white mb-6 transform-gpu">
          <span class="title-line block transform-gpu" data-text="PJM">PJM</span>
          <span class="title-line block transform-gpu text-african-gold" data-text="AFRICA">AFRICA</span>
        </h1>

        <!-- Subtitle -->
        <p class="hero-subtitle text-xl md:text-2xl text-gray-300 mb-8 transform-gpu opacity-0">
          Connecting Africans through Stories, Innovation, and Unity
        </p>

        <!-- Mission Statement -->
        <div class="hero-mission mb-8 transform-gpu opacity-0">
          <p class="text-lg font-medium text-white/90 mb-4">This isn't just media. This is a mission.</p>
          <div class="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-8 text-gray-300">
            <span>To tell our stories</span>
            <span class="hidden md:block">•</span>
            <span>To build our voice</span>
            <span class="hidden md:block">•</span>
            <span>To shape Africa's tomorrow</span>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="hero-cta flex flex-col sm:flex-row gap-4 justify-center transform-gpu opacity-0">
          <a href="/watch" class="cta-primary bg-gradient-to-r from-african-red to-african-gold text-white px-8 py-4 rounded-lg font-semibold text-lg hover:scale-105 transition-all duration-300 transform-gpu">
            Watch Stories
          </a>
          <a href="/join" class="cta-secondary border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300 transform-gpu">
            Join Movement
          </a>
        </div>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white opacity-70 animate-bounce">
      <div class="flex flex-col items-center">
        <span class="text-sm mb-2">Scroll to Explore</span>
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Particle System -->
  <div class="particles absolute inset-0 pointer-events-none">
    <div class="particle absolute w-1 h-1 bg-african-gold rounded-full opacity-60 animate-particle-1"></div>
    <div class="particle absolute w-1 h-1 bg-african-red rounded-full opacity-40 animate-particle-2"></div>
    <div class="particle absolute w-1 h-1 bg-white rounded-full opacity-30 animate-particle-3"></div>
    <div class="particle absolute w-1 h-1 bg-orange-400 rounded-full opacity-50 animate-particle-4"></div>
  </div>
</section>

<style>
  /* 3D Perspective and Transform Setup */
  .perspective-1000 {
    perspective: 1000px;
    perspective-origin: center center;
  }

  .transform-gpu {
    transform-style: preserve-3d;
    backface-visibility: hidden;
    will-change: transform;
  }

  /* Mountain Silhouette Background */
  .mountain-silhouette {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cpath d='M0,400 Q300,350 600,380 T1200,360 L1200,600 L0,600 Z' fill='%23000000' fill-opacity='0.3'/%3E%3C/svg%3E");
  }

  /* Parallax Layers */
  .layer-far {
    transform: translateZ(-100px) scale(1.1);
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .layer-mid {
    transform: translateZ(-50px) scale(1.05);
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .layer-near {
    transform: translateZ(-20px) scale(1.02);
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Hero Title 3D Effects */
  .hero-title {
    text-shadow:
      0 1px 0 #ccc,
      0 2px 0 #c9c9c9,
      0 3px 0 #bbb,
      0 4px 0 #b9b9b9,
      0 5px 0 #aaa,
      0 6px 1px rgba(0,0,0,.1),
      0 0 5px rgba(0,0,0,.1),
      0 1px 3px rgba(0,0,0,.3),
      0 3px 5px rgba(0,0,0,.2),
      0 5px 10px rgba(0,0,0,.25);
  }

  .title-line {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Content Animation States */
  .hero-content {
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hero-subtitle,
  .hero-mission,
  .hero-cta {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Floating Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotateZ(0deg); }
    50% { transform: translateY(-20px) rotateZ(5deg); }
  }

  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotateZ(0deg); }
    50% { transform: translateY(-15px) rotateZ(-3deg); }
  }

  @keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotateZ(0deg); }
    50% { transform: translateY(-10px) rotateZ(2deg); }
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 5s ease-in-out infinite 1s;
  }

  .animate-float-slow {
    animation: float-slow 6s ease-in-out infinite 2s;
  }

  /* Particle Animations */
  @keyframes particle-1 {
    0% { transform: translate(0, 100vh) scale(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translate(100px, -100px) scale(1); opacity: 0; }
  }

  @keyframes particle-2 {
    0% { transform: translate(100vw, 100vh) scale(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translate(-100px, -100px) scale(1); opacity: 0; }
  }

  @keyframes particle-3 {
    0% { transform: translate(50vw, 100vh) scale(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translate(200px, -100px) scale(1); opacity: 0; }
  }

  @keyframes particle-4 {
    0% { transform: translate(25vw, 100vh) scale(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translate(-150px, -100px) scale(1); opacity: 0; }
  }

  .animate-particle-1 {
    animation: particle-1 8s linear infinite;
    top: 100%;
    left: 10%;
  }

  .animate-particle-2 {
    animation: particle-2 10s linear infinite 2s;
    top: 100%;
    right: 10%;
  }

  .animate-particle-3 {
    animation: particle-3 12s linear infinite 4s;
    top: 100%;
    left: 50%;
  }

  .animate-particle-4 {
    animation: particle-4 9s linear infinite 6s;
    top: 100%;
    left: 25%;
  }

  /* Scroll-triggered Animation States */
  .hero-3d.scrolled .layer-far {
    transform: translateZ(-100px) scale(1.1) translateY(-20px);
  }

  .hero-3d.scrolled .layer-mid {
    transform: translateZ(-50px) scale(1.05) translateY(-40px);
  }

  .hero-3d.scrolled .layer-near {
    transform: translateZ(-20px) scale(1.02) translateY(-60px);
  }

  .hero-3d.scrolled .hero-content {
    transform: translateY(-80px) scale(0.95);
    opacity: 0.7;
  }

  /* Initial Animation States */
  .hero-3d.loaded .hero-subtitle {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.5s;
  }

  .hero-3d.loaded .hero-mission {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.8s;
  }

  .hero-3d.loaded .hero-cta {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 1.1s;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero-title {
      font-size: 3rem;
    }

    .layer-far {
      transform: translateZ(-50px) scale(1.05);
    }

    .layer-mid {
      transform: translateZ(-25px) scale(1.025);
    }

    .layer-near {
      transform: translateZ(-10px) scale(1.01);
    }
  }

  /* Accessibility - Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-float-delayed,
    .animate-float-slow,
    .animate-particle-1,
    .animate-particle-2,
    .animate-particle-3,
    .animate-particle-4,
    .animate-bounce {
      animation: none;
    }

    .parallax-layer {
      transform: none !important;
    }

    .transform-gpu {
      transform: none !important;
    }

    .hero-3d.scrolled .hero-content {
      transform: none !important;
      opacity: 1 !important;
    }
  }
</style>

<script>
  // 3D Hero Scroll Animations
  document.addEventListener('DOMContentLoaded', function() {
    const heroSection = document.querySelector('.hero-3d');
    const sceneContainer = document.querySelector('.scene-container');
    const heroContent = document.querySelector('.hero-content');
    const parallaxLayers = document.querySelectorAll('.parallax-layer');
    const titleLines = document.querySelectorAll('.title-line');

    let isScrolling = false;
    let scrollTimeout;

    // Initial load animation
    setTimeout(() => {
      heroSection?.classList.add('loaded');
    }, 500);

    // Optimized scroll handler with requestAnimationFrame
    function handleScroll() {
      if (!isScrolling) {
        requestAnimationFrame(updateParallax);
        isScrolling = true;
      }

      // Clear timeout and set new one
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        isScrolling = false;
      }, 100);
    }

    // Update parallax effects based on scroll position
    function updateParallax() {
      const scrollY = window.pageYOffset;
      const windowHeight = window.innerHeight;
      const scrollProgress = Math.min(scrollY / windowHeight, 1);

      // Add scrolled class when user scrolls
      if (scrollY > 50) {
        heroSection?.classList.add('scrolled');
      } else {
        heroSection?.classList.remove('scrolled');
      }

      // Parallax layer transformations
      parallaxLayers.forEach((layer, index) => {
        const speed = (index + 1) * 0.5;
        const yPos = scrollY * speed;
        const scale = 1 + (scrollProgress * 0.1);

        if (layer.classList.contains('layer-far')) {
          layer.style.transform = `translateZ(-100px) scale(${1.1 + scrollProgress * 0.05}) translateY(${yPos * 0.3}px)`;
        } else if (layer.classList.contains('layer-mid')) {
          layer.style.transform = `translateZ(-50px) scale(${1.05 + scrollProgress * 0.03}) translateY(${yPos * 0.6}px)`;
        } else if (layer.classList.contains('layer-near')) {
          layer.style.transform = `translateZ(-20px) scale(${1.02 + scrollProgress * 0.02}) translateY(${yPos * 0.9}px)`;
        }
      });

      // Hero content transformation
      if (heroContent) {
        const contentY = scrollY * 0.8;
        const contentScale = 1 - (scrollProgress * 0.1);
        const contentOpacity = 1 - (scrollProgress * 0.5);

        heroContent.style.transform = `translateY(${contentY}px) scale(${contentScale})`;
        heroContent.style.opacity = contentOpacity;
      }

      // Title line effects
      titleLines.forEach((line, index) => {
        const lineY = scrollY * (0.5 + index * 0.2);
        const lineRotation = scrollProgress * (index % 2 === 0 ? 2 : -2);

        line.style.transform = `translateY(${lineY}px) rotateX(${lineRotation}deg)`;
      });
    }

    // Intersection Observer for performance
    const heroObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          window.addEventListener('scroll', handleScroll, { passive: true });
        } else {
          window.removeEventListener('scroll', handleScroll);
        }
      });
    }, {
      threshold: 0.1
    });

    if (heroSection) {
      heroObserver.observe(heroSection);
    }

    // Mouse movement parallax effect
    function handleMouseMove(e) {
      if (window.innerWidth < 768) return; // Skip on mobile

      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;

      const xPos = (clientX / innerWidth - 0.5) * 2;
      const yPos = (clientY / innerHeight - 0.5) * 2;

      // Apply subtle mouse parallax to cultural elements
      const culturalElements = document.querySelectorAll('.cultural-element');
      culturalElements.forEach((element, index) => {
        const intensity = (index + 1) * 5;
        const x = xPos * intensity;
        const y = yPos * intensity;

        element.style.transform = `translate(${x}px, ${y}px)`;
      });

      // Apply mouse parallax to title
      titleLines.forEach((line, index) => {
        const intensity = (index + 1) * 2;
        const x = xPos * intensity;
        const y = yPos * intensity;

        const currentTransform = line.style.transform || '';
        const newTransform = currentTransform.replace(/translate\([^)]*\)/g, '') + ` translate(${x}px, ${y}px)`;
        line.style.transform = newTransform;
      });
    }

    // Add mouse move listener with throttling
    let mouseTimeout;
    sceneContainer?.addEventListener('mousemove', (e) => {
      clearTimeout(mouseTimeout);
      mouseTimeout = setTimeout(() => handleMouseMove(e), 16); // ~60fps
    });

    // Reset mouse effects when mouse leaves
    sceneContainer?.addEventListener('mouseleave', () => {
      const culturalElements = document.querySelectorAll('.cultural-element');
      culturalElements.forEach(element => {
        element.style.transform = '';
      });

      titleLines.forEach(line => {
        const currentTransform = line.style.transform || '';
        line.style.transform = currentTransform.replace(/translate\([^)]*\)/g, '');
      });
    });

    // Resize handler
    function handleResize() {
      // Reset transforms on resize
      updateParallax();
    }

    window.addEventListener('resize', handleResize, { passive: true });

    // Cleanup function
    window.addEventListener('beforeunload', () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      heroObserver.disconnect();
    });
  });
</script>
